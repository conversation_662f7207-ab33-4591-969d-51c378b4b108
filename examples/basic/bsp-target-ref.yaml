# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

# Example showing the new BackendSecurityPolicy targetRefs pattern
# This demonstrates the recommended Gateway API policy attachment pattern
# where policies target their resources directly using targetRefs.

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: envoy-ai-gateway-basic-openai
  namespace: default
spec:
  schema:
    name: OpenAI
  backendRef:
    name: envoy-ai-gateway-basic-openai
    kind: Backend
    group: gateway.envoyproxy.io
    # Note: backendSecurityPolicyRef is deprecated and not used in this example
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: envoy-ai-gateway-basic-aws
  namespace: default
spec:
  schema:
    name: AWSBedrock
  backendRef:
    name: envoy-ai-gateway-basic-aws
    kind: Backend
    group: gateway.envoyproxy.io
    # Note: backendSecurityPolicyRef is deprecated and not used in this example
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: envoy-ai-gateway-basic-openai-apikey
  namespace: default
spec:
  # New targetRefs pattern - policy targets the backend directly
  targetRefs:
    - group: aigateway.envoyproxy.io
      kind: AIServiceBackend
      name: envoy-ai-gateway-basic-openai
  type: APIKey
  apiKey:
    secretRef:
      name: envoy-ai-gateway-basic-openai-apikey
      namespace: default
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: envoy-ai-gateway-basic-aws-credentials
  namespace: default
spec:
  # New targetRefs pattern - policy targets the backend directly
  targetRefs:
    - group: aigateway.envoyproxy.io
      kind: AIServiceBackend
      name: envoy-ai-gateway-basic-aws
  type: AWSCredentials
  awsCredentials:
    region: us-east-1
    credentialsFile:
      secretRef:
        name: envoy-ai-gateway-basic-aws-credentials
---
# Example showing a policy targeting multiple backends
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: envoy-ai-gateway-shared-apikey
  namespace: default
spec:
  # A single policy can target multiple backends
  targetRefs:
    - group: aigateway.envoyproxy.io
      kind: AIServiceBackend
      name: envoy-ai-gateway-basic-openai
    - group: aigateway.envoyproxy.io
      kind: AIServiceBackend
      name: envoy-ai-gateway-another-backend
  type: APIKey
  apiKey:
    secretRef:
      name: envoy-ai-gateway-shared-apikey
      namespace: default
